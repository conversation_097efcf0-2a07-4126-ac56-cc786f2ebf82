import { create } from 'zustand'

type ShareVariant = 'win' | 'rank'

interface ShareDialogState {
  show: boolean
  level: number | null
  variant: ShareVariant
  open: (level: number | null, variant?: ShareVariant) => void
  close: () => void
}

export const useShareDialogStore = create<ShareDialogState>((set) => ({
  show: false,
  level: null,
  variant: 'win',
  open: (level, variant = 'win') => set({ show: true, level, variant }),
  close: () => set({ show: false, level: null, variant: 'win' })
}))
