{"name": "btaskeemidfall", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-separator": "^1.1.7", "@tanstack/react-query": "^5.89.0", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html-to-image": "^1.11.13", "lucide-react": "^0.543.0", "next": "15.5.2", "react": "19.1.0", "react-countdown": "^2.3.6", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5"}}