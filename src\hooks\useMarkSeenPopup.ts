import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useNativeStore } from '@/stores/useNativeStore'

interface MarkSeenPopupRequest {
  userId: string
  gameCampaignId: string
  missionGameId: string[]
  token?: string
}

export function useMarkSeenPopup() {
  const token = useNativeStore((s) => s.token)

  return useMutation({
    mutationFn: async (payload: MarkSeenPopupRequest) => {
      const { data } = await axios.post('/api/markSeenPopup', { ...payload, token })
      return data
    }
  })
}
