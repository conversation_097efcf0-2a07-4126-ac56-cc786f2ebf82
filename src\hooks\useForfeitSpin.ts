// src/hooks/useForfeitSpin.ts
import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { ForfeitSpinRequest, ForfeitSpinResponse } from '@/app/api/forfeitSpin/route'
import { useNativeStore } from '@/stores/useNativeStore'

export function useForfeitSpin() {
  const token = useNativeStore((s) => s.token)

  return useMutation<ForfeitSpinResponse, Error, ForfeitSpinRequest>({
    mutationFn: async (payload: ForfeitSpinRequest) => {
      const { data } = await axios.post(
        '/api/forfeitSpin',
        { ...payload, token },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      return data
    },
    onError: (error) => {
      console.error('Forfeit spin mutation error:', error)
    }
  })
}
