// src/app/api/getAlertPopup/route.ts
import { NextResponse } from 'next/server'
import axios from 'axios'

const BE_URL = process.env.BE_URL
const ACCESS_KEY_BE = process.env.ACCESS_KEY_BE

export async function POST(req: Request) {
  try {
    const body = await req.json()
    console.log('Incoming Request Body:', body)

    // Get token from request body or use fallback
    const authToken =
      body.token ||
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTIwNDcwNH0.EZRGk3-m8VIdmFxW4WMUJU1Mjhi-P8aOQRfaeLoKVko'

    if (!BE_URL || !ACCESS_KEY_BE) {
      throw new Error('Missing BE_URL or ACCESS_KEY_BE in environment variables')
    }

    const { data } = await axios.post(`${BE_URL}/api-asker-vn/get-alert-popup`, body, {
      headers: {
        accessKey: ACCESS_KEY_BE,
        Authorization: `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    })

    console.log('Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (err: unknown) {
    if (axios.isAxiosError(err)) {
      console.error('Axios error:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      })
      return NextResponse.json({ error: err.message }, { status: err.response?.status ?? 500 })
    }

    console.error('Unknown error:', err)
    return NextResponse.json({ error: 'Request failed' }, { status: 500 })
  }
}
