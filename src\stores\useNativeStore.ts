// src/store/useNativeStore.ts
import { create } from 'zustand'

interface NativeState {
  token: string | null
  locale: string | null
  userId: string | null
  versionAppName: string | null
  top: string | null
  bottom: string | null
  timeoutReached: boolean
  setNativeData: (data: {
    token: string
    locale: string
    versionAppName: string
    userId: string
    top?: string
    bottom?: string
  }) => void
  setTimeoutReached: () => void
}

export const useNativeStore = create<NativeState>((set) => ({
  token: null,
  locale: null,
  userId: null,
  versionAppName: null,
  timeoutReached: false,
  top: null,
  bottom: null,
  setNativeData: ({ token, locale, versionAppName, userId, top, bottom }) =>
    set({ token, locale, versionAppName, userId, top: top || null, bottom: bottom || null }),
  setTimeoutReached: () => set({ timeoutReached: true })
}))
