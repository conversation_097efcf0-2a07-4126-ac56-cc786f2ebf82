// src/app/api/win-mini-game/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface WinMiniGameRequest {
  userId: string
  level: number
}

interface WinMiniGameResponse {
  isWinning: boolean
}

export async function POST(req: NextRequest) {
  try {
    const accessKey = req.headers.get('accesskey')

    if (!accessKey) {
      return NextResponse.json({ error: 'Missing required header: accessKey' }, { status: 400 })
    }

    if (accessKey !== process.env.ACCESS_KEY_FE) {
      return NextResponse.json({ error: 'Invalid access key' }, { status: 401 })
    }

    const body: WinMiniGameRequest = await req.json()
    console.log('Win Mini Game API - Incoming Request:', { accessKey, ...body })

    if (!body.userId || typeof body.level !== 'number') {
      return NextResponse.json({ error: 'Missing required fields: userId, level' }, { status: 400 })
    }

    // Check if user has completed this level
    // The user is considered "winning" if they have passed the level
    // This handles both cases: just completed (current level = level + 1) and previously completed
    const userData = UserLevelManager.getUserData(body.userId)
    let isWinning = false

    if (userData) {
      // User is winning if they have passed this level
      const hasPassedLevel = userData.passedLevels.includes(body.level)
      // Also check if they're currently on this level (for edge cases)
      const isOnCurrentLevel = userData.currentLevel === body.level
      isWinning = hasPassedLevel || isOnCurrentLevel

      console.log('Win Mini Game API - User Data:', {
        userId: body.userId,
        requestedLevel: body.level,
        userCurrentLevel: userData.currentLevel,
        userPassedLevels: userData.passedLevels,
        hasPassedLevel,
        isOnCurrentLevel,
        isWinning
      })
    } else {
      console.log('Win Mini Game API - No user data found for userId:', body.userId)
    }

    const response: WinMiniGameResponse = { isWinning }

    console.log('Win Mini Game API - Response:', response)
    return NextResponse.json(response)
  } catch (error) {
    console.error('Win Mini Game API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
