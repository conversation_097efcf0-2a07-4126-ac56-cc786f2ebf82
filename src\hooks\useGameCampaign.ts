import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useApiCallGuardStore } from '@/stores/useApiCallGuardStore'

interface GameCampaignRequest {
  userId: string
  appVersion: string
  from: string
}

// src/hooks/useGameCampaign.ts
export interface GameCampaignResponse {
  _id: string
  avatar: string
  checkListMission: {
    canSharePostToday: boolean
    isCheckInToday: boolean
    isFriendDoneTaskFirst: boolean
    isJoinFirstGame: boolean
  }
  furthestLevel: number
  infoLevels: {
    level: number
    score: number
    rewardInfo: {
      icon: string
      title: Record<string, string>
    }
  }[]
  isPlayedGame: boolean
  numberOfTurn: number
  numberOfGift: number
  username: string
}

export const useGameCampaign = () => {
  const { startApiCall, finishApiCall } = useApiCallGuardStore()

  return useMutation({
    mutationFn: async (payload: GameCampaignRequest) => {
      const callId = `gameCampaign-${payload.userId}-${payload.from}`

      // Check if this API call is already in progress
      if (!startApiCall(callId)) {
        throw new Error('Game campaign API call already in progress')
      }

      try {
        const { data } = await axios.post('/api/gameCampaign', payload)
        return data
      } finally {
        finishApiCall(callId)
      }
    }
  })
}
