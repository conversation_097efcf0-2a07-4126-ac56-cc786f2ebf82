// src/app/api/win-passed-game/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface VerifyPassedLevelRequest {
  userId: string
  level: number
}

interface VerifyPassedLevelResponse {
  isWinning: boolean
}

export async function POST(req: NextRequest) {
  try {
    const accessKey = req.headers.get('accesskey')

    if (!accessKey) {
      return NextResponse.json({ error: 'Missing required header: accessKey' }, { status: 400 })
    }

    if (accessKey !== process.env.ACCESS_KEY_FE) {
      return NextResponse.json({ error: 'Invalid access key' }, { status: 401 })
    }

    const body: VerifyPassedLevelRequest = await req.json()
    console.log('Verify Passed Level API - Incoming Request:', { accessKey, ...body })

    if (!body.userId || typeof body.level !== 'number') {
      return NextResponse.json({ error: 'Missing required fields: userId, level' }, { status: 400 })
    }

    const isWinning = UserLevelManager.hasUserPassedLevel(body.userId, body.level)
    const response: VerifyPassedLevelResponse = { isWinning }

    console.log('Verify Passed Level API - Response:', response)
    return NextResponse.json(response)
  } catch (error) {
    console.error('Verify Passed Level API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
