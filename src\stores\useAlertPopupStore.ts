import { create } from 'zustand'

export interface Mission {
  _id: string
  action: string
  serviceName?: Record<string, string>
}

export interface AlertPopup {
  completeMissions: Mission[]
  topic: string
  totalSpin: number
}

interface AlertPopupState {
  popup: AlertPopup | null
  setPopup: (popup: AlertPopup) => void
  clearPopup: () => void
}

export const useAlertPopupStore = create<AlertPopupState>((set) => ({
  popup: null,
  setPopup: (popup) => set({ popup }),
  clearPopup: () => set({ popup: null })
}))
