// src/constants.ts
export const BOARD_WIDTH = 7
export const BOARD_HEIGHT = 10
export const PIECE_TYPES = 10
export const GAP = 4

export const CELL_WIDTH = (342 - (BOARD_WIDTH - 1) * GAP) / BOARD_WIDTH
export const CELL_HEIGHT = (563 - (BOARD_HEIGHT - 1) * GAP) / BOARD_HEIGHT

export interface Point {
  x: number
  y: number
}
export interface GameState {
  board: number[][]
  selectedPiece: Point | null
  level: number
  lives: number
  isGameOver: boolean
  isWon: boolean
  path: Point[]
  hintCount: number
  hintedPair: WrongPair | null
}
export interface WrongPair {
  a: Point
  b: Point
}

export const stageConfig = [
  { stage: 1, position: 'bottom-[4.75%]' },
  { stage: 2, position: 'right-[22%] bottom-[5.25%]' },
  { stage: 3, position: '-right-[4%] bottom-[10.5%]' },
  { stage: 4, position: 'right-[0%] bottom-[18.25%]' },
  { stage: 5, position: 'right-[30%] bottom-[20.5%]' },
  { stage: 6, position: '-left-[2%] bottom-[25%]' },
  { stage: 7, position: 'left-[13%] bottom-[32.25%]' },
  { stage: 8, position: 'right-[13%] bottom-[34.5%]' },
  { stage: 9, position: '-right-[2.5%] bottom-[42%]' },
  { stage: 10, position: 'right-[25%] bottom-[46.5%]' },
  { stage: 11, position: 'left-[4%] bottom-[50%]' },
  { stage: 12, position: 'left-[11%] bottom-[57%]' },
  { stage: 13, position: 'right-[19%] bottom-[58.5%]' },
  { stage: 14, position: '-right-[5%] bottom-[64.5%]' },
  { stage: 15, position: 'right-[17%] bottom-[69%]' },
  { stage: 16, position: 'left-[12%] bottom-[70.5%]' },
  { stage: 17, position: '-left-[8%] bottom-[76%]' },
  { stage: 18, position: 'left-[19%] bottom-[79.25%]' },
  { stage: 19, position: 'right-[14%] bottom-[83.5%]' },
  { stage: 20, position: 'right-[27%] bottom-[91.75%]' }
]
