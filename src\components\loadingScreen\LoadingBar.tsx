// src/components/loadingScreen/LoadingBar.tsx
'use client'

import React, { useEffect } from 'react'
import { Progress } from '@/components/ui/progress'
import { useLoadingProgress } from '@/hooks/useLoadingProgress'

export default function LoadingBar({ onProgress }: { onProgress?: (n: number) => void }) {
  const { progress } = useLoadingProgress(10)

  useEffect(() => {
    if (onProgress) onProgress(progress)
  }, [progress, onProgress])

  return (
    <>
      <div className='absolute inset-0 top-[51.75%] left-[14.5%] w-[65.75%]'>
        <Progress value={progress} className='h-[19%] w-full bg-transparent' />
      </div>

      <div className='font-bdstreet absolute top-[37%] right-[17.5%] flex h-[12.5%] w-[7%] items-center justify-center text-[13px] font-bold text-[#FF8228]'>
        {progress}%
      </div>

      <div className='absolute top-[53%] left-[18%] z-20 h-[3%] w-[50%] rounded-full bg-white/50' />
    </>
  )
}
