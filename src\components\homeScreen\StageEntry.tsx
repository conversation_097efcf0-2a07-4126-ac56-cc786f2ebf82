// src/components/homeScreen/StageEntry.tsx
import React, { forwardRef } from 'react'
import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'

interface StageEntryProps {
  stage: number
  position: string
  onClick: (stage: number) => void
  variant?: 'completed' | 'current' | 'unreached'
}

const StageEntry = forwardRef<HTMLDivElement, StageEntryProps>(
  ({ stage, position, onClick, variant = 'completed' }, ref) => {
    const settings = {
      completed: {
        img: '/home/<USER>',
        ratio: 543 / 532,
        textColor: 'text-[#00A464]',
        textSize: 'text-[24px]',
        textOpacity: '',
        scale: 'scale-100'
      },
      current: {
        img: '/home/<USER>',
        ratio: 1,
        textColor: 'text-[#C43C00]',
        textSize: 'text-[32px]',
        textOpacity: '',
        scale: 'scale-[1.6] translate-y-[2%]'
      },
      unreached: {
        img: '/home/<USER>',
        ratio: 1,
        textColor: 'text-white',
        textSize: 'text-[24px]',
        textOpacity: 'opacity-50',
        scale: 'scale-[0.6] translate-y-[2.5%] translate-x-[0.25%]'
      }
    }[variant]

    return (
      <div ref={ref} className={`absolute ${position} w-[40%]`}>
        <AspectRatio ratio={settings.ratio}>
          <div className='relative h-full w-full'>
            <Image
              src={settings.img}
              alt={`Stage ${variant} ${stage}`}
              fill
              quality={100}
              className={`transform object-contain ${settings.scale}`}
            />
            <div
              className='absolute top-[28.5%] left-[25.5%] z-20 flex h-[50%] w-[50%] cursor-pointer items-center justify-center'
              onClick={() => variant !== 'unreached' && onClick(stage)}
            >
              <span
                className={`font-bdstreet text-shadow-fancy ${settings.textSize} ${settings.textColor} ${settings.textOpacity}`}
              >
                {stage}
              </span>
            </div>
          </div>
        </AspectRatio>
      </div>
    )
  }
)
StageEntry.displayName = 'StageEntry'
export default StageEntry
