'use client'

import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import MediumerOrangeButton from '@/assets/drawer/mediumerOrangeButton.svg'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'

interface GiveUpDialogProps {
  open: boolean
  onClose: () => void
  onGiveUp: () => void
  onContinue: () => void
  stage: number
}

export default function GiveUpDialog({ open, onClose, onGiveUp, onContinue, stage }: GiveUpDialogProps) {
  return (
    <Dialog open={open} onOpenChange={(o) => !o && onClose()}>
      <DialogContent className='border-none bg-transparent p-0 shadow-none' showCloseButton={false}>
        <AspectRatio ratio={1560 / 2720} className='relative'>
          <Image src='/dialog/giveUpBG.png' alt='Time up background' fill quality={100} />
          {/* Centered content */}
          <div className='relative top-[41%] flex h-full w-full justify-center'>
            <div className='flex h-[25%] w-1/2 flex-col items-center justify-center gap-[15%]'>
              <h2 className='font-signwriter text-[32px] text-[#FFFF70]'>Màn {stage}</h2>
              <div className='flex flex-col items-center justify-center gap-[2%]'>
                <p className='font-montserrat text-center text-[12px] font-medium text-white'>
                  Bạn rời khỏi trò chơi trước khi hoàn tất?
                </p>
                <p className='font-montserrat text-center text-[12px] font-medium text-white'>
                  Hệ thống không lưu kết quả và bạn phải chơi lại từ đầu.
                </p>
              </div>
            </div>
          </div>
          <div className='absolute top-[69.5%] left-1/2 flex h-[7.5%] w-full -translate-x-1/2 items-center justify-center space-x-[7%]'>
            <div onClick={onGiveUp} className='relative w-[33%] cursor-pointer'>
              <AspectRatio ratio={137 / 48} className='relative'>
                <MediumerOrangeButton className='h-full w-full' />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Bỏ cuộc
              </span>
            </div>

            <div className='relative w-[33%]' onClick={onContinue}>
              <AspectRatio ratio={137 / 48} className='relative'>
                <MediumGreenButton className='h-full w-full' />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Chơi tiếp
              </span>
            </div>
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  )
}
