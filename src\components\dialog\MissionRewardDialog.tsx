// src/components/dialog/MissionRewardDialog.tsx
'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { ScrollArea } from '@/components/ui/scroll-area'
import HomeIcon from '@/assets/dialog/home.svg'
import Stamp from '@/assets/dialog/stamp.svg'
import { useAlertPopupStore } from '@/stores/useAlertPopupStore'
import { useGameStore } from '@/stores/useGameStore'
import { useMarkSeenPopup } from '@/hooks/useMarkSeenPopup'
import { useNativeStore } from '@/stores/useNativeStore'

interface MissionRewardDialogProps {
  open: boolean
  onClose: () => void
  rewardCount: number
}

export default function MissionRewardDialog({ open, onClose, rewardCount }: MissionRewardDialogProps) {
  const { popup } = useAlertPopupStore()
  const { gameCampaignId } = useGameStore()
  const { userId } = useNativeStore()
  const { mutateAsync: markSeenPopup } = useMarkSeenPopup()

  const handleClose = async () => {
    try {
      if (gameCampaignId && userId && popup?.completeMissions?.length) {
        // ✅ send ALL missions, not deduplicated
        await markSeenPopup({
          userId,
          gameCampaignId,
          missionGameId: popup.completeMissions.map((m) => m._id)
        })
      }
    } catch (err) {
      console.error('markSeenPopup failed:', err)
    } finally {
      onClose()
    }
  }

  const missionItems: Record<string, string> = {
    DONE_TASK: '- Bạn đã hoàn thành công việc',
    DONE_TASK_REFERRAL: '- Bạn bè hoàn thành đơn hàng đầu tiên',
    REFERRAL: '- Giới thiệu bạn bè đăng ký tài khoản thành công',
    JOIN_FIRST_GAME: '- Truy cập vào game lần đầu tiên',
    SHARE_POST: '- Chia sẻ thành tích lên community',
    REDEEM_DEAL: '- Đổi bReward thành công'
  }

  // ✅ For display: only show one entry per action
  const displayActions = Array.from(new Set(popup?.completeMissions.map((m) => m.action))).filter(
    (a) => missionItems[a]
  )

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent showCloseButton={false} className='border-none bg-transparent p-0 shadow-none'>
        <AspectRatio ratio={1560 / 2720} className='relative'>
          <Image src='/dialog/missionBG.png' alt='Mission Reward' fill quality={100} />
          <div className='font-montserrat relative top-[43%] flex h-full w-full justify-center text-[12px]'>
            <div className='flex h-[27%] w-1/2 flex-col items-center gap-[12%]'>
              <div className='w-[35%]'>
                <AspectRatio ratio={955 / 968} className='relative'>
                  <Image
                    src='/dialog/mooncake.png'
                    alt='Reward'
                    fill
                    quality={100}
                    className='drop-shadow-[5px_2px_20px_rgba(0,0,0,0.3)]'
                  />
                  <div className='absolute right-0 bottom-0 w-[30%]'>
                    <AspectRatio ratio={1} className='relative'>
                      <div className='absolute inset-0 flex items-center justify-center'>
                        <Stamp className='h-full w-full' />
                      </div>
                      <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 mt-[10%] ml-[5%] flex items-center justify-center text-[10px] font-bold text-white'>
                        x{rewardCount}
                      </span>
                    </AspectRatio>
                  </div>
                </AspectRatio>
              </div>

              <div className='flex w-full flex-col items-center justify-center gap-[4%]'>
                <p className='px-[10%] text-center text-[12px] font-bold text-white'>
                  Chúc mừng bạn đã hoàn thành nhiệm vụ:
                </p>
                <ScrollArea className='h-[60px] w-full'>
                  <ul className='space-y-1 text-center text-[12px] text-white'>
                    {displayActions.map((action) => (
                      <li key={action}>{missionItems[action]}</li>
                    ))}
                  </ul>
                </ScrollArea>
              </div>
            </div>
          </div>

          <div className='absolute top-[69.5%] left-1/2 flex h-[7.5%] w-[26%] -translate-x-1/2 items-center justify-center'>
            <div className='z-30 w-[10.6dvw]'>
              <AspectRatio ratio={1}>
                <div
                  className='relative flex h-full w-full cursor-pointer items-center justify-center'
                  onClick={handleClose}
                >
                  <Image src='/dialog/roundButtonBG.png' alt='Reward' fill quality={100} />
                  <HomeIcon className='z-20 h-[50%] w-[50%]' />
                </div>
              </AspectRatio>
            </div>
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  )
}
