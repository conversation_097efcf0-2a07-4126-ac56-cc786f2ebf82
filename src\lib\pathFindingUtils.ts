// pathFindingUtils.ts
import { Point, BOARD_WIDTH, BOARD_HEIGHT, WrongPair } from '@/lib/constants'
export function findPath(board: number[][], start: Point, end: Point): Point[] | null {
  if (start.x === end.x && start.y === end.y) return null
  if (board[start.y]?.[start.x] === undefined || board[end.y]?.[end.x] === undefined) return null
  if (board[start.y][start.x] === 0 || board[end.y][end.x] === 0) return null
  if (board[start.y][start.x] !== board[end.y][end.x]) return null
  const dirs = [
    [0, 1],
    [0, -1],
    [1, 0],
    [-1, 0]
  ] as const
  type Node = { point: Point; path: Point[]; turns: number }
  const q: Node[] = [{ point: start, path: [start], turns: 0 }]
  const visited = new Set<string>([`${start.x},${start.y}`])
  while (q.length) {
    const { point, path, turns } = q.shift()!
    for (const [dx, dy] of dirs) {
      let nx = point.x,
        ny = point.y
      const basePath = [...path]
      let nTurns = turns
      if (path.length > 1) {
        const prevDir = {
          x: path[path.length - 1].x - path[path.length - 2].x,
          y: path[path.length - 1].y - path[path.length - 2].y
        }
        if (prevDir.x !== dx || prevDir.y !== dy) nTurns++
      }
      if (nTurns > 2) continue
      while (true) {
        nx += dx
        ny += dy
        if (nx < -1 || nx > BOARD_WIDTH || ny < -1 || ny > BOARD_HEIGHT) break
        const key = `${nx},${ny}`
        if (nx === end.x && ny === end.y) return [...basePath, { x: nx, y: ny }]
        if (nx >= 0 && nx < BOARD_WIDTH && ny >= 0 && ny < BOARD_HEIGHT) {
          if (board[ny][nx] !== 0) break
          if (!visited.has(key)) {
            visited.add(key)
            q.push({ point: { x: nx, y: ny }, path: [...basePath, { x: nx, y: ny }], turns: nTurns })
          }
        } else {
          if (!visited.has(key)) {
            visited.add(key)
            q.push({ point: { x: nx, y: ny }, path: [...basePath, { x: nx, y: ny }], turns: nTurns })
          }
        }
      }
    }
  }
  return null
}
export function hasValidMoves(board: number[][]): boolean {
  for (let r1 = 0; r1 < BOARD_HEIGHT; r1++) {
    for (let c1 = 0; c1 < BOARD_WIDTH; c1++) {
      if (board[r1][c1] === 0) continue
      for (let r2 = 0; r2 < BOARD_HEIGHT; r2++) {
        for (let c2 = 0; c2 < BOARD_WIDTH; c2++) {
          if (board[r2][c2] === 0) continue
          if (r1 === r2 && c1 === c2) continue
          if (board[r1][c1] === board[r2][c2]) {
            if (findPath(board, { x: c1, y: r1 }, { x: c2, y: r2 })) return true
          }
        }
      }
    }
  }
  return false
}
export function getAllMatchablePairs(board: number[][]): WrongPair[] {
  const pairs: WrongPair[] = []
  for (let r1 = 0; r1 < BOARD_HEIGHT; r1++) {
    for (let c1 = 0; c1 < BOARD_WIDTH; c1++) {
      const val = board[r1][c1]
      if (val === 0) continue
      for (let r2 = r1; r2 < BOARD_HEIGHT; r2++) {
        const startC2 = r2 === r1 ? c1 + 1 : 0
        for (let c2 = startC2; c2 < BOARD_WIDTH; c2++) {
          if (board[r2][c2] === val) {
            const path = findPath(board, { x: c1, y: r1 }, { x: c2, y: r2 })
            if (path) {
              pairs.push({ a: { x: c1, y: r1 }, b: { x: c2, y: r2 } })
            }
          }
        }
      }
    }
  }
  return pairs
}
export function getRandomMatchablePair(board: number[][]): WrongPair | null {
  const pairs = getAllMatchablePairs(board)
  if (pairs.length === 0) return null
  const index = Math.floor(Math.random() * pairs.length)
  return pairs[index]
}
