import { create } from 'zustand'

interface ApiCallState {
  // Track which levels have had their reward APIs called
  calledLevels: Set<number>
  // Track ongoing API calls to prevent multiple simultaneous calls
  ongoingCalls: Set<string>
  
  // Actions
  markLevelCalled: (level: number) => void
  hasLevelBeenCalled: (level: number) => boolean
  startApiCall: (callId: string) => boolean // returns false if already ongoing
  finishApiCall: (callId: string) => void
  reset: () => void
}

export const useApiCallGuardStore = create<ApiCallState>((set, get) => ({
  calledLevels: new Set(),
  ongoingCalls: new Set(),
  
  markLevelCalled: (level: number) => {
    set((state) => ({
      calledLevels: new Set([...state.calledLevels, level])
    }))
  },
  
  hasLevelBeenCalled: (level: number) => {
    return get().calledLevels.has(level)
  },
  
  startApiCall: (callId: string) => {
    const { ongoingCalls } = get()
    if (ongoingCalls.has(callId)) {
      return false // Call already ongoing
    }
    
    set((state) => ({
      ongoingCalls: new Set([...state.ongoingCalls, callId])
    }))
    return true // Call can proceed
  },
  
  finishApiCall: (callId: string) => {
    set((state) => {
      const newOngoingCalls = new Set(state.ongoingCalls)
      newOngoingCalls.delete(callId)
      return { ongoingCalls: newOngoingCalls }
    })
  },
  
  reset: () => {
    set({
      calledLevels: new Set(),
      ongoingCalls: new Set()
    })
  }
}))
