import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { AlertPopup } from '@/stores/useAlertPopupStore'
import { useNativeStore } from '@/stores/useNativeStore'

interface Payload {
  userId: string
  gameCampaignId: string
  token?: string
}

export function useAlertPopup() {
  const token = useNativeStore((s) => s.token)

  return useMutation<AlertPopup[], Error, Payload>({
    mutationFn: async (payload: Payload) => {
      const { data } = await axios.post('/api/getAlertPopup', { ...payload, token })
      return data
    }
  })
}
