// src/stores/useGameStore.ts
import { create } from 'zustand'
import { GameCampaignResponse } from '@/hooks/useGameCampaign'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface PassedLevel {
  level: number
  score: number
  rewardInfo: {
    icon: string
    title: Record<string, string>
  }
}

interface GameState {
  gameCampaignId: string | null
  avatar: string | null
  canSharePostToday: boolean
  isFriendDoneTaskFirst: boolean
  isJoinedGame: boolean
  isPlayedGame: boolean
  currentLevel: number
  turns: number
  gifts: number
  hasLoggedInToday: boolean
  username: string | null
  passedLevels: PassedLevel[]

  setCurrentLevel: (level: number) => void
  setTurns: (turns: number) => void
  setGifts: (gifts: number) => void
  setHasLoggedInToday: (value: boolean) => void
  setGameCampaign: (payload: GameCampaignResponse) => void
  initializeUserLevelTracking: (userId: string) => void
  updateUserLevelProgress: (userId: string, level: number, isCompleting?: boolean) => void
}

export const useGameStore = create<GameState>((set) => ({
  gameCampaignId: 'abcd',
  avatar: 'https://www.shutterstock.com/image-vector/vector-flat-illustration-grayscale-avatar-600nw-2281862025.jpg',
  canSharePostToday: false,
  isFriendDoneTaskFirst: false,
  isJoinedGame: false,
  isPlayedGame: false,
  currentLevel: 10,
  turns: 0,
  gifts: 0,
  hasLoggedInToday: false,
  username: 'Cô Hai Báo',
  passedLevels: [],

  setCurrentLevel: (level) => set({ currentLevel: level }),
  setTurns: (turns) => set({ turns }),
  setGifts: (gifts) => set({ gifts }),
  setHasLoggedInToday: (value) => set({ hasLoggedInToday: value }),
  setGameCampaign: (payload) =>
    set({
      gameCampaignId: payload._id,
      avatar: payload.avatar,
      canSharePostToday: payload.checkListMission.canSharePostToday,
      hasLoggedInToday: payload.checkListMission.isCheckInToday,
      isFriendDoneTaskFirst: payload.checkListMission.isFriendDoneTaskFirst,
      isJoinedGame: payload.checkListMission.isJoinFirstGame,
      isPlayedGame: payload.isPlayedGame,
      currentLevel: payload.furthestLevel,
      turns: payload.numberOfTurn,
      gifts: payload.numberOfGift,
      username: payload.username,
      passedLevels: (payload.infoLevels ?? []).map((lvl) => ({
        level: lvl.level,
        score: lvl.score,
        rewardInfo: lvl.rewardInfo
      }))
    }),
  initializeUserLevelTracking: (userId: string) => {
    const state = useGameStore.getState()
    if (state.gameCampaignId) {
      const passedLevelNumbers = state.passedLevels.map((lvl) => lvl.level)
      UserLevelManager.initializeUserFromGameCampaign(userId, state.currentLevel, passedLevelNumbers)
    }
  },
  updateUserLevelProgress: (userId: string, level: number, isCompleting: boolean = false) => {
    UserLevelManager.updateUserLevel(userId, level, isCompleting)

    // Update local state if user is progressing to a new level
    if (isCompleting) {
      set((state) => ({
        currentLevel: Math.max(state.currentLevel, level + 1)
      }))
    }
  }
}))
