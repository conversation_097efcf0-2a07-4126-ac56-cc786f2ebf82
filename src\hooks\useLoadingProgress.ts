// src/hooks/useLoadingProgress.ts
'use client'
import { useEffect, useState } from 'react'

let hasLoadedOnce = false

export function useLoadingProgress(startAt = 10) {
  const [progress, setProgress] = useState(hasLoadedOnce ? 100 : startAt)

  useEffect(() => {
    if (hasLoadedOnce) return

    const totalDuration = 2000 + Math.random() * 500

    const weights = Array.from({ length: 4 }, () => Math.random())
    const weightSum = weights.reduce((a, b) => a + b, 0)
    const stepDurations = weights.map((w) => (w / weightSum) * totalDuration)

    const randoms = Array.from({ length: 3 }, () => Math.floor(Math.random() * (100 - startAt) + startAt)).sort(
      (a, b) => a - b
    )

    const steps = [startAt, ...randoms, 100]

    let elapsed = 0
    steps.forEach((value, i) => {
      setTimeout(() => {
        setProgress(value)
        if (i === steps.length - 1) {
          hasLoadedOnce = true
        }
      }, elapsed)

      if (i < stepDurations.length) {
        elapsed += stepDurations[i]
      }
    })
  }, [startAt])

  return { progress }
}
