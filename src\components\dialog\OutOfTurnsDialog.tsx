'use client'

import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import MediumLightOrangeButton from '@/assets/dialog/mediumLightOrangeButton.svg'

interface OutOfTurnsDialogProps {
  open: boolean
  onClose: () => void
}

export default function OutOfTurnsDialog({ open, onClose }: OutOfTurnsDialogProps) {
  return (
    <Dialog open={open}>
      <DialogContent showCloseButton={false} className='border-none bg-transparent p-0 shadow-none'>
        <AspectRatio ratio={1560 / 2720} className='relative'>
          <Image src='/dialog/outOfTurnBG.png' alt='Out of turns background' fill quality={100} />
          <div className='font-montserrat relative top-[41%] flex h-full w-full justify-center'>
            <div className='flex h-[25%] w-1/2 flex-col items-center justify-center space-y-[1%]'>
              <p className='text-center text-[12px] font-medium text-white'>Hi<PERSON><PERSON> bạn không có đủ</p>
              <p className='text-center text-[12px] font-bold text-[#FFFF70]'>Bánh trung thu.</p>
              <p className='text-center text-[12px] font-medium text-white'>Hoàn thành nhiệm vụ để</p>
              <p className='text-center text-[12px] font-medium text-white'>tiếp tục nhé!</p>
            </div>
          </div>

          <div onClick={onClose} className='absolute top-[69.5%] left-1/2 h-auto w-[35%] -translate-x-1/2'>
            <AspectRatio ratio={136 / 48} className='relative w-full'>
              <MediumLightOrangeButton className='absolute inset-0 h-full w-full' />
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Nhiệm vụ
              </span>
            </AspectRatio>
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  )
}
