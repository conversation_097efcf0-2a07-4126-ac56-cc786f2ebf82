import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { useNativeStore } from '@/stores/useNativeStore'

interface UsePresentUnboxingRequest {
  gameCampaignId: string
  from: string
  levelNumber: number
  timeCompleted: number
  userId: string
  token?: string
}

export interface UsePresentUnboxingResponse {
  image: string
  rewardKey: number
  segment: number
  score: number
  title: {
    en: string
    id: string
    ko: string
    th: string
    vi: string
  }
}

export function usePresentUnboxing() {
  const token = useNativeStore((s) => s.token)

  return useMutation<UsePresentUnboxingResponse, Error, UsePresentUnboxingRequest>({
    mutationFn: async (payload: UsePresentUnboxingRequest) => {
      const { data } = await axios.post(
        '/api/use-present-unboxing',
        { ...payload, token },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      return data
    },
    onError: (error) => {
      console.error('Present unboxing mutation error:', error)
    },
    onSuccess: (data) => {
      console.log('Present unboxing successful:', data)
    }
  })
}
