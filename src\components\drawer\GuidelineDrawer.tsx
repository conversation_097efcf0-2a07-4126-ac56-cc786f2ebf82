'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON><PERSON><PERSON>, Drawer<PERSON>lose, DrawerFooter } from '@/components/ui/drawer'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { ScrollArea } from '@/components/ui/scroll-area'
import ReactMarkdown from 'react-markdown'
import LargeOrangeButton from '@/assets/drawer/largeOrangeButton.svg'
import React, { useState, useEffect } from 'react'

interface GuidelineDrawerProps {
  open: boolean
  onClose: () => void
}

const GuidelineDrawer: React.FC<GuidelineDrawerProps> = ({ open, onClose }) => {
  const [markdownContent, setMarkdownContent] = useState('')

  useEffect(() => {
    // Fetch the markdown content
    fetch('/guideline/vi.md')
      .then((response) => response.text())
      .then((text) => setMarkdownContent(text))
      .catch((error) => console.error('Error loading markdown:', error))
  }, [])

  return (
    <Drawer open={open} onOpenChange={onClose}>
      <DrawerContent className='flex h-[97.5dvh] flex-col overflow-hidden'>
        {/* Fixed header */}
        <DrawerHeader className='h-[18.5%]'>
          <DrawerTitle className='font-bdstreet text-border-orange text-[32px]'>Thể lệ</DrawerTitle>
        </DrawerHeader>

        {/* Scrollable content */}
        <div className='flex h-[70.5%] flex-1 items-end overflow-hidden'>
          <div className='mx-auto h-[97%] w-[91.79%] rounded-3xl border border-white/30 bg-white/10 backdrop-blur-xs'>
            <ScrollArea className='h-full w-full p-[4%]'>
              <div className='font-montserrat text-white'>
                <ReactMarkdown
                  components={{
                    strong: ({ children }) => {
                      const text = String(children)
                      if (text.includes('GỌI ÁNH TRĂNG VỀ') || text.includes('15/09/2025 - 15/10/2025')) {
                        return <span className='font-bold text-[#FF5500]'>{children}</span>
                      }
                      return <strong>{children}</strong>
                    },
                    em: ({ children }) => {
                      const text = String(children)
                      if (text.includes('Bảng Nhiệm Vụ Hằng Ngày.')) {
                        return <span className='text-[#FF5500] italic'>{children}</span>
                      }
                      if (text.includes('Ưu đãi của tôi')) {
                        return <span className='text-[#FB712C] italic'>{children}</span>
                      }
                      if (text.includes('Lưu ý: Apple không tham gia tài trợ chương trình này.')) {
                        return <span className='text-[#DACC5F] italic'>{children}</span>
                      }
                      return <em>{children}</em>
                    },
                    p: ({ children }) => (
                      <p className='mb-4 text-justify text-[14px] text-white'>
                        {React.Children.map(children, (child) => {
                          if (typeof child === 'string') {
                            if (child.includes('1900.636.736')) {
                              return <span className='text-[#FA702A]'>{child}</span>
                            }
                            if (child.includes('<EMAIL>')) {
                              return <span className='text-[#FA702A] italic'>{child}</span>
                            }
                          }
                          return child
                        })}
                      </p>
                    ),
                    li: ({ children }) => (
                      <li className='mb-2 ml-4 list-none text-justify text-[14px] text-white'>
                        {React.Children.map(children, (child) => {
                          if (typeof child === 'string') {
                            if (child.includes('1900.636.736')) {
                              return <span className='text-[#FA702A]'>{child}</span>
                            }
                            if (child.includes('<EMAIL>')) {
                              return <span className='text-[#FA702A] italic'>{child}</span>
                            }
                          }
                          return child
                        })}
                      </li>
                    )
                  }}
                >
                  {markdownContent}
                </ReactMarkdown>
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Footer */}
        <DrawerFooter className='flex h-[11%] items-center justify-center pb-[5%]'>
          <DrawerClose className='w-full'>
            <AspectRatio ratio={326 / 48} className='relative mx-auto w-[90%]'>
              <LargeOrangeButton className='absolute inset-0 h-full w-full' />
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                Đóng
              </span>
            </AspectRatio>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default GuidelineDrawer
