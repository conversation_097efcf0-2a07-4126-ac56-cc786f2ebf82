import { BOARD_WIDTH, BOARD_HEIGHT, PIECE_TYPES, CELL_WIDTH, CELL_HEIGHT, GAP } from '@/lib/constants'

export function swap(board: number[][], x1: number, y1: number, x2: number, y2: number) {
  const tmp = board[y1][x1]
  board[y1][x1] = board[y2][x2]
  board[y2][x2] = tmp
}

export function generateBoard(): number[][] {
  const totalCells = BOARD_WIDTH * BOARD_HEIGHT
  if (totalCells % 2 !== 0) {
    throw new Error(`Board size must be even. Got ${totalCells}`)
  }

  const pieces: number[] = []
  const pairsNeeded = totalCells / 2
  const basePairsPerType = Math.floor(pairsNeeded / PIECE_TYPES)
  const extraPairs = pairsNeeded % PIECE_TYPES

  for (let type = 1; type <= PIECE_TYPES; type++) {
    for (let i = 0; i < basePairsPerType * 2; i++) {
      pieces.push(type)
    }
  }

  const types = [...Array(PIECE_TYPES).keys()].map((i) => i + 1)
  for (let i = 0; i < extraPairs; i++) {
    const t = types[Math.floor(Math.random() * types.length)]
    pieces.push(t, t)
  }

  for (let i = pieces.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[pieces[i], pieces[j]] = [pieces[j], pieces[i]]
  }

  const board: number[][] = []
  let idx = 0
  for (let r = 0; r < BOARD_HEIGHT; r++) {
    const row: number[] = []
    for (let c = 0; c < BOARD_WIDTH; c++) row.push(pieces[idx++])
    board.push(row)
  }
  return board
}

export function findCentre(i: number, j: number) {
  return {
    x: i * (CELL_WIDTH + GAP) + CELL_WIDTH / 2,
    y: j * (CELL_HEIGHT + GAP) + CELL_HEIGHT / 2
  }
}

/**
 * Maps stage number to fixType based on the specified mapping:
 * - level 1-2 -> fixType 1
 * - level 3-4 -> fixType 2
 * - level 5-6 -> fixType 3
 * - level 7-8 -> fixType 4
 * - level 9-10 -> fixType 5
 * - level 11-12 -> fixType 6
 * - level 13-14 -> fixType 7
 * - level 15-16 -> fixType 8
 * - level > 16 -> fixType 9
 */
export function getFixTypeFromStage(stage: number): number {
  const type = ((stage - 1) % 5) + 1
  return type
}

export function fixMatrix(board: number[][], fixType: number): number[][] {
  function fixZone(board: number[][], x1: number, y1: number, x2: number, y2: number, vector: number) {
    let stop: boolean
    const UU = [1, -1, 0, 0]
    const VV = [0, 0, 1, -1]

    do {
      stop = true
      for (let y = y1; y <= y2; y++) {
        for (let x = x1; x <= x2; x++) {
          if (board[y][x] > 0) {
            const nx = x + UU[vector]
            const ny = y + VV[vector]
            if (nx >= 0 && nx < BOARD_WIDTH && ny >= 0 && ny < BOARD_HEIGHT) {
              if (board[ny][nx] === 0) {
                swap(board, x, y, nx, ny)
                stop = false
              }
            }
          }
        }
      }
    } while (!stop)
  }

  const newBoard = board.map((r) => [...r])

  if (fixType === 1) return newBoard
  if (fixType === 2) fixZone(newBoard, 0, 0, 7, 9, 0)
  else if (fixType === 3) fixZone(newBoard, 0, 0, 7, 9, 1)
  else if (fixType === 4) fixZone(newBoard, 0, 0, 7, 9, 2)
  else if (fixType === 5) fixZone(newBoard, 0, 0, 7, 9, 3)
  else if (fixType === 6) {
    fixZone(newBoard, 0, 5, 7, 9, 0)
    fixZone(newBoard, 0, 0, 7, 4, 1)
  } else if (fixType === 7) {
    fixZone(newBoard, 0, 5, 7, 9, 1)
    fixZone(newBoard, 0, 0, 7, 4, 0)
  } else if (fixType === 8) {
    fixZone(newBoard, 0, 0, 3, 9, 2)
    fixZone(newBoard, 4, 0, 7, 9, 3)
  } else {
    fixZone(newBoard, 0, 0, 3, 9, 3)
    fixZone(newBoard, 4, 0, 7, 9, 2)
  }
  return newBoard
}
