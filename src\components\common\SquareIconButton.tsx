// src/components/common/SquareIconButton.tsx
import React from 'react'
import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'

interface SquareIconButtonProps {
  icon: string
  alt: string
  onClick: () => void
  className?: string
}

const SquareIconButton: React.FC<SquareIconButtonProps> = ({ icon, alt, onClick, className }) => {
  return (
    <div className='w-[15%]'>
      <AspectRatio ratio={1}>
        <div className='relative flex h-full w-full cursor-pointer items-center justify-center' onClick={onClick}>
          <Image src='/home/<USER>' alt={`${alt} background`} fill quality={100} />
          <div className={`absolute z-20 ${className}`}>
            <Image src={icon} alt={alt} fill quality={100} />
          </div>
        </div>
      </AspectRatio>
    </div>
  )
}

export default SquareIconButton
