// src/app/api/calculate-score/route.ts
import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface CalculateScoreRequest {
  userId: string
  gameCampaignId: string
  levelNumber: number
  timeCompleted: number
  token?: string
}

interface CalculateScoreResponse {
  image: string
  rewardKey: number
  segment: number
  score: number
  title: {
    en: string
    id: string
    ko: string
    th: string
    vi: string
  }
}

export async function POST(req: NextRequest) {
  try {
    const body: CalculateScoreRequest = await req.json()
    console.log('Calculate Score API - Incoming Request:', body)

    // Validate required fields
    if (
      !body.userId ||
      !body.gameCampaignId ||
      typeof body.levelNumber !== 'number' ||
      typeof body.timeCompleted !== 'number'
    ) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, gameCampaignId, levelNumber, timeCompleted' },
        { status: 400 }
      )
    }

    // Verify that the user has passed this level (anti-cheat check)
    const hasPassedLevel = UserLevelManager.hasUserPassedLevel(body.userId, body.levelNumber)
    if (!hasPassedLevel) {
      console.warn(`User ${body.userId} attempted to calculate score for level ${body.levelNumber} without passing it`)
      return NextResponse.json({ error: 'User has not passed this level' }, { status: 403 })
    }

    // Read env values
    const BE_URL = process.env.BE_URL
    const ACCESS_KEY_BE = process.env.ACCESS_KEY_BE

    if (!BE_URL || !ACCESS_KEY_BE) {
      throw new Error('Missing BE_URL or ACCESS_KEY_BE in environment variables')
    }

    // Get token from request body or use fallback
    const authToken =
      body.token ||
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTIwNDcwNH0.EZRGk3-m8VIdmFxW4WMUJU1Mjhi-P8aOQRfaeLoKVko'

    // Call the external calculate-score API with headers
    const { data } = await axios.post<CalculateScoreResponse>(`${BE_URL}/event-vn/calculate-score`, body, {
      headers: {
        'Content-Type': 'application/json',
        accessKey: ACCESS_KEY_BE,
        Authorization: `Bearer ${authToken}`
      }
    })

    console.log('Calculate Score API - External API Response:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Calculate Score API error:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json(
      { error: err.message || 'Failed to calculate score' },
      { status: err.response?.status ?? 500 }
    )
  }
}
