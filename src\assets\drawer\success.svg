<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_263_29650)">
<g filter="url(#filter0_dii_263_29650)">
<path d="M23.987 2.48809C23.9477 2.75371 23.8217 3.00896 23.6033 3.20179C18.3967 7.78879 14.5821 13.974 10.8219 21.627C10.5769 22.1252 10.0921 22.4417 9.5645 22.4986C9.02532 22.5574 8.48906 22.6584 7.96435 22.8022C7.24837 22.9975 6.49772 22.6633 6.12848 21.985C4.62198 19.2239 2.64563 16.7504 0.322593 14.7214C-0.028161 14.4153 -0.102748 13.8803 0.1475 13.4788C0.156723 13.4629 0.166561 13.4476 0.176399 13.4317C1.50894 11.32 4.40408 11.2318 5.85515 13.2542C6.56995 14.2494 7.30731 15.2618 7.97306 16.4015C11.3079 10.834 16.7896 4.21111 22.4106 1.26511C22.729 1.09803 23.1046 1.10229 23.4189 1.27979C23.857 1.52772 24.0546 2.02349 23.987 2.48809Z" fill="url(#paint0_linear_263_29650)"/>
</g>
</g>
<defs>
<filter id="filter0_dii_263_29650" x="-2" y="-0.857422" width="32" height="29.7148" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_263_29650"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_263_29650" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1.55"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_263_29650"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_263_29650" result="effect3_innerShadow_263_29650"/>
</filter>
<linearGradient id="paint0_linear_263_29650" x1="12" y1="1.14314" x2="21.1429" y2="12.5717" gradientUnits="userSpaceOnUse">
<stop stop-color="#21FFA8"/>
<stop offset="1" stop-color="#00A464"/>
</linearGradient>
<clipPath id="clip0_263_29650">
<rect width="24" height="23.9995" fill="white"/>
</clipPath>
</defs>
</svg>
