import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { GetHistoryGameCampaignResponse, HistoryEntry } from '@/app/api/get-history-game-campaign/route'
import { useNativeStore } from '@/stores/useNativeStore'

interface GetHistoryGameCampaignRequest {
  userId: string
  gameCampaignId: string
  token?: string
}

export function useHistoryGameCampaign() {
  const token = useNativeStore((s) => s.token)

  return useMutation<GetHistoryGameCampaignResponse, Error, GetHistoryGameCampaignRequest>({
    mutationFn: async (payload: GetHistoryGameCampaignRequest) => {
      const { data } = await axios.post(
        '/api/get-history-game-campaign',
        { ...payload, token },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      return data
    },
    onError: (error) => {
      console.error('Get history game campaign mutation error:', error)
    }
  })
}

export type { HistoryEntry, GetHistoryGameCampaignResponse }
