@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* ✅ Added overscroll + lock rules */
html,
body {
  overscroll-behavior: none;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

@layer base {
  * {
    @apply border-border outline-ring/50 select-none;
  }

  body {
    @apply bg-background text-foreground;
  }

  img {
    @apply select-none;
    -webkit-user-drag: none;
    user-drag: none;
  }

  input,
  textarea {
    @apply select-text; /* allow text selection inside inputs */
  }
}

.font-bdstreet {
  font-family: var(--font-bd-street), sans-serif;
}

.font-signwriter {
  font-family: var(--font-signwriter), sans-serif;
}

.font-montserrat {
  font-family: var(--font-montserrat), sans-serif;
}

@utility drop-shadow-up-2xl {
  filter: drop-shadow(0 -8px 8px rgba(0, 0, 0, 0.25));
}

@utility drop-shadow-down-2xl {
  filter: drop-shadow(0 8px 8px rgba(0, 0, 0, 0.25));
}

html,
body {
  touch-action: pan-x pan-y;
}

html,
body {
  -ms-content-zooming: none; /* IE/Edge */
  -ms-touch-action: pan-x pan-y;
}

@layer utilities {
  .text-shadow-fancy {
    text-shadow:
      /* Drop shadow (outer) */
      1px 1px 1px rgba(0, 0, 0, 0.2),
      /* Fake inner shadow dark (slight offset up/left) */ -0.59px -1.18px 1.71px rgba(0, 0, 0, 0.2),
      /* Fake inner shadow light (slight offset down/right) */ 1.18px 0.59px 1.12px rgba(216, 216, 216, 0.35);
  }
}

.drop-shadow-orange-btn {
  filter: drop-shadow(0px -7px 30px rgba(249, 249, 249, 0.3));
}

.text-shadow-orange-btn {
  text-shadow: 0px 2px 2.6px rgba(0, 0, 0, 0.25);
}

.text-stroke {
  -webkit-text-stroke: 1px #ffecce; /* for WebKit browsers */
  text-stroke: 1px #ffecce; /* future standard */
}

.piece-shadow {
  box-shadow: 2px 2px 4.8px rgba(0, 0, 0, 0.2);
}

@layer utilities {
  .score-shadow {
    /* Drop shadow */
    text-shadow:
      2px 2px 4px rgba(0, 0, 0, 0.25),
      /* Inner shadows simulated using multiple shadows */ -1px -1px 3.1px rgba(0, 0, 0, 0.21),
      1px 1px 0 rgba(255, 255, 255, 0.55);
  }
}

.text-border-orange {
  position: relative;
  color: #ff5500;
  text-shadow:
    -0.9px -0.9px 0 #ffecce,
    0.9px -0.9px 0 #ffecce,
    -0.9px 0.9px 0 #ffecce,
    0.9px 0.9px 0 #ffecce,
    0 2px 0 #0a650e;
}
