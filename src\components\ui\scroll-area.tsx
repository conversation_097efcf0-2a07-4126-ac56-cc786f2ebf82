// src/components/ui/scroll-area.tsx
'use client'

import * as React from 'react'
import * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area'
import { cn } from '@/lib/utils'

interface ScrollAreaProps extends React.ComponentProps<typeof ScrollAreaPrimitive.Root> {
  viewportRef?: React.Ref<HTMLDivElement>
  overscroll?: 'auto' | 'none' | 'contain' | 'initial' // optional prop
}

const ScrollArea = ({
  className,
  children,
  viewportRef,
  overscroll = 'auto', // default behavior
  ...props
}: ScrollAreaProps) => (
  <ScrollAreaPrimitive.Root data-slot='scroll-area' className={cn('relative', className)} {...props}>
    <ScrollAreaPrimitive.Viewport
      ref={viewportRef}
      data-slot='scroll-area-viewport'
      className={cn(
        'size-full overflow-y-auto rounded-[inherit] transition-[color,box-shadow] outline-none',
        overscroll === 'none' ? 'overscroll-none' : '',
        overscroll === 'contain' ? 'overscroll-contain' : '',
        overscroll === 'initial' ? 'overscroll-auto' : ''
      )}
    >
      {children}
    </ScrollAreaPrimitive.Viewport>
    <ScrollBar />
    <ScrollAreaPrimitive.Corner />
  </ScrollAreaPrimitive.Root>
)

function ScrollBar({
  className,
  orientation = 'vertical',
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {
  return (
    <ScrollAreaPrimitive.ScrollAreaScrollbar orientation={orientation} className={cn('hidden', className)} {...props}>
      <ScrollAreaPrimitive.ScrollAreaThumb className='hidden' />
    </ScrollAreaPrimitive.ScrollAreaScrollbar>
  )
}

export { ScrollArea, ScrollBar }
