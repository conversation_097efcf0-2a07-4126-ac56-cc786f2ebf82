// src/components/common/RoundIconButton.tsx
import React from 'react'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import RoundButtonBG from '@/assets/home/<USER>'

interface RoundIconButtonProps {
  icon: React.ReactNode
  onClick: () => void
  className?: string
}

const RoundIconButton: React.FC<RoundIconButtonProps> = ({ icon, onClick, className }) => {
  return (
    <div className={`z-30 w-[10.6%] ${className || ''}`}>
      <AspectRatio ratio={1}>
        <div className='relative flex h-full w-full cursor-pointer items-center justify-center' onClick={onClick}>
          <RoundButtonBG className='absolute z-0 h-full w-full' />
          {icon}
        </div>
      </AspectRatio>
    </div>
  )
}

export default RoundIconButton
