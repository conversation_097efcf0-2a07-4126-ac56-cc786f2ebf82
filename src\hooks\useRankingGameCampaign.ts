'use client'

import { useQuery } from '@tanstack/react-query'
import axios from 'axios'
import { useGameStore } from '@/stores/useGameStore'
import { useNativeStore } from '@/stores/useNativeStore'

export interface RankingResponse {
  avatar: string
  createdAt: string
  isTarget: boolean
  name: string
  rank: number
  score: {
    maxLevel: number
    total: number
  }
}

export function useRankingGameCampaign(userId: string | null) {
  const gameCampaignId = useGameStore((s) => s.gameCampaignId)
  const token = useNativeStore((s) => s.token)

  return useQuery<RankingResponse[], Error>({
    queryKey: ['ranking-game-campaign', userId, gameCampaignId],
    queryFn: async () => {
      if (!userId || !gameCampaignId) return []
      const { data } = await axios.post('/api/getRankingGameCampaign', { userId, gameCampaignId, token })
      return data
    },
    enabled: false, // do not auto-run
    refetchOnWindowFocus: false
  })
}
