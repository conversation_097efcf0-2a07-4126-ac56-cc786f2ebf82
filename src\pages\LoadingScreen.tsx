// src/components/loadingScreen/LoadingScreen.tsx
'use client'

import Image from 'next/image'
import React, { useEffect, useCallback, useRef } from 'react'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import ProgressBarSvg from '@/assets/loading/progressBar.svg'
import LoadingBar from '@/components/loadingScreen/LoadingBar'
import { useLanguageStore } from '@/stores/useLanguageStore'
import { useGameStore } from '@/stores/useGameStore'
import { useGameCampaign } from '@/hooks/useGameCampaign'
import { useNativeStore } from '@/stores/useNativeStore'
import ClientOnly from '@/components/common/ClientOnly'

interface LoadingScreenProps {
  onProgress?: (value: number) => void
  onSuccess?: () => void
  onError?: () => void
  tokenLoading?: boolean
  hasToken?: boolean
}

const LoadingScreenContent: React.FC<LoadingScreenProps> = ({
  onProgress,
  onSuccess,
  onError,
  tokenLoading,
  hasToken
}) => {
  const { mutateAsync } = useGameCampaign()
  const setGameCampaign = useGameStore((state) => state.setGameCampaign)
  const initializeUserLevelTracking = useGameStore((state) => state.initializeUserLevelTracking)
  const { userId, versionAppName } = useNativeStore()

  // Use refs to track if we've already made the API call
  const hasCalledApiRef = useRef(false)

  // Memoize callbacks to prevent unnecessary re-renders
  const handleSuccess = useCallback(() => {
    onSuccess?.()
  }, [onSuccess])

  const handleError = useCallback(() => {
    onError?.()
  }, [onError])

  useEffect(() => {
    const fetchCampaign = async () => {
      // Wait for native token data before making API calls
      if (tokenLoading || !hasToken || !userId || !versionAppName) {
        return
      }

      // Prevent multiple API calls
      if (hasCalledApiRef.current) {
        return
      }

      hasCalledApiRef.current = true

      try {
        const data = await mutateAsync({
          userId,
          appVersion: versionAppName,
          from: 'WEBVIEW'
        })
        console.log('loading')
        setGameCampaign(data)

        // Initialize user level tracking for anti-cheat system
        initializeUserLevelTracking(userId)

        handleSuccess()
      } catch (err) {
        console.error('Failed to load campaign:', err)
        // Reset the flag on error so user can retry
        hasCalledApiRef.current = false
        handleError()
      }
    }
    fetchCampaign()
  }, [
    // Only include essential dependencies that should trigger a re-fetch
    tokenLoading,
    hasToken,
    userId,
    versionAppName,
    mutateAsync,
    setGameCampaign,
    initializeUserLevelTracking,
    handleSuccess,
    handleError
  ])

  const language = useLanguageStore((state) => state.language)
  const titleSrc = {
    EN: '/loading/titleEN.png',
    KO: '/loading/titleKO.png',
    VN: '/loading/titleVN.png'
  }[language]
  const widthClass = language === 'VN' ? 'w-[80%]' : 'w-[57.5%]'

  return (
    <div className='relative h-[100dvh] w-[100dvw] overflow-hidden'>
      <Image src='/loading/loadingBG.webp' alt='Background' fill className='object-cover' priority quality={100} />
      <div
        className={`absolute top-[6%] left-1/2 flex h-[39%] -translate-x-1/2 items-center justify-center ${widthClass}`}
      >
        <AspectRatio ratio={1986 / 1170} className='h-auto w-full'>
          <Image src={titleSrc} alt={`Title ${language}`} fill className='object-contain' priority quality={100} />
        </AspectRatio>
      </div>
      <div className='absolute bottom-0 w-full translate-x-[2.5%]'>
        <AspectRatio ratio={372 / 155}>
          <div className='relative h-full w-full'>
            <ProgressBarSvg className='h-full w-full' />
            <LoadingBar onProgress={onProgress} />
          </div>
        </AspectRatio>
      </div>
    </div>
  )
}

const LoadingScreen: React.FC<LoadingScreenProps> = (props) => {
  return (
    <ClientOnly fallback={<div className='h-screen w-screen bg-white' />}>
      <LoadingScreenContent {...props} />
    </ClientOnly>
  )
}

export default LoadingScreen
